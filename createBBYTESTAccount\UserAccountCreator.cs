using System;
using System.IO;

namespace CreateBBYTESTAccount
{
    public class UserAccountCreator
    {
        private readonly string _windowsPath;
        private RegistryHelper _registryHelper;

        public UserAccountCreator(string windowsPath)
        {
            _windowsPath = windowsPath;
        }

        /// <summary>
        /// Validates that the Windows installation path is valid
        /// </summary>
        /// <returns>True if valid Windows installation</returns>
        public bool ValidateWindowsPath()
        {
            try
            {
                if (string.IsNullOrEmpty(_windowsPath) || !Directory.Exists(_windowsPath))
                {
                    return false;
                }

                // Check for essential Windows directories and files
                string system32Path = Path.Combine(_windowsPath, "System32");
                string configPath = Path.Combine(system32Path, "config");
                string samPath = Path.Combine(configPath, "SAM");
                string securityPath = Path.Combine(configPath, "SECURITY");

                return Directory.Exists(system32Path) && 
                       Directory.Exists(configPath) && 
                       File.Exists(samPath) && 
                       File.Exists(securityPath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Creates a new user account in the offline Windows installation
        /// </summary>
        /// <param name="username">Username for the new account</param>
        /// <param name="password">Password for the new account</param>
        /// <param name="fullName">Full name for the new account</param>
        /// <param name="isAdmin">Whether the account should have administrator privileges</param>
        /// <param name="progressCallback">Callback for progress updates</param>
        /// <returns>True if successful</returns>
        public bool CreateUserAccount(string username, string password, string fullName, bool isAdmin, Action<string> progressCallback = null)
        {
            try
            {
                progressCallback?.Invoke("Validating Windows installation...");
                
                if (!ValidateWindowsPath())
                {
                    throw new Exception("Invalid Windows installation path");
                }

                progressCallback?.Invoke("Validating input parameters...");
                
                if (string.IsNullOrWhiteSpace(username))
                {
                    throw new ArgumentException("Username cannot be empty");
                }

                if (username.Length > 20)
                {
                    throw new ArgumentException("Username cannot be longer than 20 characters");
                }

                if (string.IsNullOrEmpty(fullName))
                {
                    fullName = username;
                }

                progressCallback?.Invoke("Initializing registry helper...");
                
                _registryHelper = new RegistryHelper(_windowsPath);

                progressCallback?.Invoke("Loading SAM registry hive...");
                
                if (!_registryHelper.LoadSAMHive())
                {
                    throw new Exception("Failed to load SAM registry hive");
                }

                progressCallback?.Invoke("Loading SECURITY registry hive...");
                
                if (!_registryHelper.LoadSecurityHive())
                {
                    throw new Exception("Failed to load SECURITY registry hive");
                }

                progressCallback?.Invoke("Getting machine SID...");
                
                string machineSid = _registryHelper.GetMachineSid();

                progressCallback?.Invoke("Checking existing users...");
                
                uint[] existingRids = _registryHelper.GetExistingUserRids();

                progressCallback?.Invoke("Generating new user RID...");
                
                uint newRid = SecurityHelper.GenerateNextRid(existingRids);

                progressCallback?.Invoke($"Creating user account with RID {newRid}...");
                
                if (!_registryHelper.CreateUserAccount(username, password, fullName, isAdmin, newRid))
                {
                    throw new Exception("Failed to create user account in registry");
                }

                progressCallback?.Invoke("Saving changes to registry...");
                
                _registryHelper.SaveAndUnloadHives();

                progressCallback?.Invoke("User account created successfully!");
                
                return true;
            }
            catch (Exception ex)
            {
                progressCallback?.Invoke($"Error: {ex.Message}");
                
                // Clean up
                try
                {
                    _registryHelper?.Dispose();
                }
                catch
                {
                    // Ignore cleanup errors
                }
                
                throw;
            }
            finally
            {
                _registryHelper?.Dispose();
                _registryHelper = null;
            }
        }

        /// <summary>
        /// Gets information about the Windows installation
        /// </summary>
        /// <returns>Windows version information</returns>
        public string GetWindowsVersionInfo()
        {
            try
            {
                if (!ValidateWindowsPath())
                {
                    return "Invalid Windows installation";
                }

                // Try to read version information from the registry
                // This is a simplified approach
                string system32Path = Path.Combine(_windowsPath, "System32");
                
                if (Directory.Exists(system32Path))
                {
                    // Check for Windows 11 specific files/directories
                    string[] win11Indicators = {
                        "dwm.exe",
                        "explorer.exe",
                        "winlogon.exe"
                    };

                    bool hasWin11Files = true;
                    foreach (string file in win11Indicators)
                    {
                        if (!File.Exists(Path.Combine(system32Path, file)))
                        {
                            hasWin11Files = false;
                            break;
                        }
                    }

                    if (hasWin11Files)
                    {
                        return "Windows 11 (detected)";
                    }
                    else
                    {
                        return "Windows (version unknown)";
                    }
                }

                return "Unknown Windows version";
            }
            catch
            {
                return "Error detecting Windows version";
            }
        }

        /// <summary>
        /// Checks if the current process has the necessary privileges
        /// </summary>
        /// <returns>True if running with sufficient privileges</returns>
        public static bool HasRequiredPrivileges()
        {
            try
            {
                // In WinPE, we typically have full access
                // This is a basic check
                return Environment.UserName.Equals("SYSTEM", StringComparison.OrdinalIgnoreCase) ||
                       IsRunningAsAdministrator();
            }
            catch
            {
                return false;
            }
        }

        private static bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
