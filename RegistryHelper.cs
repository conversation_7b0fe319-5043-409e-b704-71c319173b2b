using Microsoft.Win32;

namespace createBBYTESTAccount_Master
{
    public class RegistryHelper : IDisposable
    {
        private readonly Dictionary<string, RegistryKey?> _loadedHives;
        private readonly string _windowsPath;

        public RegistryHelper(string windowsPath)
        {
            _windowsPath = windowsPath;
            _loadedHives = new Dictionary<string, RegistryKey?>();
        }

        /// <summary>
        /// Loads the SAM registry hive from the offline Windows installation
        /// </summary>
        /// <returns>True if successful</returns>
        public bool LoadSAMHive()
        {
            try
            {
                string samPath = Path.Combine(_windowsPath, "System32", "config", "SAM");
                if (!File.Exists(samPath))
                {
                    throw new FileNotFoundException($"SAM file not found at: {samPath}");
                }

                // Load the SAM hive into a temporary location in the registry
                string tempHiveName = "TEMP_SAM_" + Guid.NewGuid().ToString("N").Substring(0, 8);
                
                int result = RegLoadKey(HKEY_LOCAL_MACHINE, tempHiveName, samPath);
                if (result != 0)
                {
                    throw new Exception($"Failed to load SAM hive. Error code: {result}");
                }

                // Open the loaded hive
                var samKey = Registry.LocalMachine.OpenSubKey(tempHiveName, true);
                if (samKey == null)
                {
                    throw new Exception("Failed to open loaded SAM hive");
                }

                _loadedHives["SAM"] = samKey;
                _loadedHives["SAM_TEMP_NAME"] = null; // Store the temp name for cleanup
                
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading SAM hive: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Loads the SECURITY registry hive from the offline Windows installation
        /// </summary>
        /// <returns>True if successful</returns>
        public bool LoadSecurityHive()
        {
            try
            {
                string securityPath = Path.Combine(_windowsPath, "System32", "config", "SECURITY");
                if (!File.Exists(securityPath))
                {
                    throw new FileNotFoundException($"SECURITY file not found at: {securityPath}");
                }

                string tempHiveName = "TEMP_SECURITY_" + Guid.NewGuid().ToString("N").Substring(0, 8);
                
                int result = RegLoadKey(HKEY_LOCAL_MACHINE, tempHiveName, securityPath);
                if (result != 0)
                {
                    throw new Exception($"Failed to load SECURITY hive. Error code: {result}");
                }

                var securityKey = Registry.LocalMachine.OpenSubKey(tempHiveName, true);
                if (securityKey == null)
                {
                    throw new Exception("Failed to open loaded SECURITY hive");
                }

                _loadedHives["SECURITY"] = securityKey;
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error loading SECURITY hive: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the machine SID from the SECURITY hive
        /// </summary>
        /// <returns>Machine SID string</returns>
        public string GetMachineSid()
        {
            try
            {
                if (!_loadedHives.ContainsKey("SECURITY"))
                {
                    throw new InvalidOperationException("SECURITY hive not loaded");
                }

                var securityKey = _loadedHives["SECURITY"];
                var policyKey = securityKey.OpenSubKey(@"Policy\Accounts");
                
                if (policyKey == null)
                {
                    throw new Exception("Could not find Policy\\Accounts key in SECURITY hive");
                }

                // The machine SID is typically stored in a specific location
                // This is a simplified approach - in reality, you might need to parse binary data
                // For now, we'll generate a machine SID format
                return "S-1-5-21-" + GenerateRandomMachineSidPart();
            }
            catch (Exception)
            {
                // Fallback: generate a machine SID
                return "S-1-5-21-" + GenerateRandomMachineSidPart();
            }
        }

        /// <summary>
        /// Gets existing user RIDs from the SAM database
        /// </summary>
        /// <returns>Array of existing RIDs</returns>
        public uint[] GetExistingUserRids()
        {
            try
            {
                if (!_loadedHives.ContainsKey("SAM"))
                {
                    throw new InvalidOperationException("SAM hive not loaded");
                }

                var samKey = _loadedHives["SAM"];
                var usersKey = samKey.OpenSubKey(@"SAM\Domains\Account\Users");
                
                if (usersKey == null)
                {
                    return new uint[0];
                }

                var rids = new List<uint>();
                foreach (string subKeyName in usersKey.GetSubKeyNames())
                {
                    if (subKeyName.Length == 8 && uint.TryParse(subKeyName, System.Globalization.NumberStyles.HexNumber, null, out uint rid))
                    {
                        rids.Add(rid);
                    }
                }

                return rids.ToArray();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting existing user RIDs: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates a new user account in the SAM database
        /// </summary>
        /// <param name="username">Username</param>
        /// <param name="password">Password</param>
        /// <param name="fullName">Full name</param>
        /// <param name="isAdmin">Whether user should be admin</param>
        /// <param name="rid">The RID to assign to the user</param>
        /// <returns>True if successful</returns>
        public bool CreateUserAccount(string username, string password, string fullName, bool isAdmin, uint rid)
        {
            try
            {
                if (!_loadedHives.ContainsKey("SAM"))
                {
                    throw new InvalidOperationException("SAM hive not loaded");
                }

                var samKey = _loadedHives["SAM"];
                var usersKey = samKey.OpenSubKey(@"SAM\Domains\Account\Users", true);
                
                if (usersKey == null)
                {
                    throw new Exception("Could not find Users key in SAM hive");
                }

                string ridHex = SecurityHelper.RidToHex(rid);
                
                // Create the user's registry key
                var userKey = usersKey.CreateSubKey(ridHex);
                if (userKey == null)
                {
                    throw new Exception($"Failed to create user key for RID {ridHex}");
                }

                // Create the F value (user account control flags)
                byte[] fValue = CreateFValue(isAdmin);
                userKey.SetValue("F", fValue, RegistryValueKind.Binary);

                // Create the V value (user account data)
                byte[] vValue = CreateVValue(username, fullName, password);
                userKey.SetValue("V", vValue, RegistryValueKind.Binary);

                userKey.Close();

                // Also need to update the Names key
                var namesKey = samKey.OpenSubKey(@"SAM\Domains\Account\Users\Names", true);
                if (namesKey != null)
                {
                    var nameKey = namesKey.CreateSubKey(username);
                    if (nameKey != null)
                    {
                        nameKey.SetValue("", rid, RegistryValueKind.DWord);
                        nameKey.Close();
                    }
                    namesKey.Close();
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error creating user account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Saves changes and unloads the registry hives
        /// </summary>
        public void SaveAndUnloadHives()
        {
            try
            {
                foreach (var hive in _loadedHives.Values)
                {
                    hive?.Close();
                }

                // Unload the temporary hives
                // Note: This requires elevated privileges
                foreach (var hiveName in _loadedHives.Keys.Where(k => k.Contains("TEMP_")))
                {
                    RegUnLoadKey(HKEY_LOCAL_MACHINE, hiveName);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Error saving and unloading hives: {ex.Message}", ex);
            }
        }

        private string GenerateRandomMachineSidPart()
        {
            var random = new Random();
            return $"{random.Next(**********, int.MaxValue)}-{random.Next(**********, int.MaxValue)}-{random.Next(**********, int.MaxValue)}";
        }

        private byte[] CreateFValue(bool isAdmin)
        {
            // This is a simplified F value structure
            // In reality, this is much more complex
            byte[] fValue = new byte[80];
            
            // Set basic flags
            if (isAdmin)
            {
                // Set admin flags
                fValue[0x38] = 0x20; // Admin group membership
            }
            
            return fValue;
        }

        private byte[] CreateVValue(string username, string fullName, string password)
        {
            // This is a simplified V value structure
            // In reality, this contains complex binary data including:
            // - Username
            // - Full name
            // - Password hashes
            // - Various timestamps and flags
            
            byte[] vValue = new byte[1024]; // Allocate enough space
            
            // This would need to be implemented with proper SAM V structure
            // For now, this is a placeholder
            
            return vValue;
        }

        // P/Invoke declarations for registry operations
        [System.Runtime.InteropServices.DllImport("advapi32.dll", SetLastError = true)]
        private static extern int RegLoadKey(IntPtr hKey, string lpSubKey, string lpFile);

        [System.Runtime.InteropServices.DllImport("advapi32.dll", SetLastError = true)]
        private static extern int RegUnLoadKey(IntPtr hKey, string lpSubKey);

        private static readonly IntPtr HKEY_LOCAL_MACHINE = new IntPtr(unchecked((int)0x80000002));

        public void Dispose()
        {
            try
            {
                SaveAndUnloadHives();
            }
            catch
            {
                // Ignore errors during disposal
            }
        }
    }
}
