namespace createBBYTESTAccount_Master
{
    public partial class MainForm : Form
    {
        private UserAccountCreator? _accountCreator;

        public MainForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Check if running in WinPE environment
            bool isWinPE = IsRunningInWinPE();

            if (!isWinPE)
            {
                // Only show privilege warnings if NOT in WinPE
                if (!UserAccountCreator.HasRequiredPrivileges())
                {
                    MessageBox.Show(
                        "This application requires administrator privileges to modify registry hives.\n\n" +
                        "Please run as administrator or from WinPE environment.\n\n" +
                        "The application will continue but may not function properly without elevated privileges.",
                        "Insufficient Privileges",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                }
            }
            else
            {
                // Running in WinPE - show helpful info
                MessageBox.Show(
                    "WinPE Environment Detected\n\n" +
                    "You can now create user accounts in offline Windows installations.\n" +
                    "Select the Windows folder of the offline installation (e.g., C:\\Windows).",
                    "WinPE Mode",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }

            // Try to detect common Windows installation paths
            try
            {
                string[] commonPaths = {
                    @"C:\Windows",
                    @"D:\Windows",
                    @"E:\Windows"
                };

                foreach (string path in commonPaths)
                {
                    if (Directory.Exists(path))
                    {
                        txtWindowsPath.Text = path;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // If we can't detect paths, just leave the field empty
                System.Diagnostics.Debug.WriteLine($"Error detecting Windows paths: {ex.Message}");
            }

            UpdateUI();
        }

        private void btnBrowseWindows_Click(object sender, EventArgs e)
        {
            try
            {
                // Try the modern folder browser first (Windows 10+)
                if (TryUseModernFolderBrowser())
                    return;

                // Fallback to classic folder browser
                using (var folderDialog = new FolderBrowserDialog())
                {
                    folderDialog.Description = "Select the Windows installation directory (e.g., C:\\Windows)";
                    folderDialog.ShowNewFolderButton = false;
                    folderDialog.RootFolder = Environment.SpecialFolder.MyComputer;

                    // Set initial directory
                    string initialPath = @"C:\";
                    if (!string.IsNullOrEmpty(txtWindowsPath.Text) && Directory.Exists(txtWindowsPath.Text))
                    {
                        initialPath = txtWindowsPath.Text;
                    }
                    folderDialog.SelectedPath = initialPath;

                    DialogResult result = folderDialog.ShowDialog(this);
                    if (result == DialogResult.OK && !string.IsNullOrEmpty(folderDialog.SelectedPath))
                    {
                        txtWindowsPath.Text = folderDialog.SelectedPath;
                        UpdateUI();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"Folder browser error: {ex.Message}\n\n" +
                    "Please manually enter the Windows path:\n" +
                    "• C:\\Windows (if Windows is on C: drive)\n" +
                    "• D:\\Windows (if Windows is on D: drive)\n" +
                    "• [Drive]:\\Windows (for other drives)",
                    "Folder Browser Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                // Focus on the text box for manual entry
                txtWindowsPath.Focus();
            }
        }

        private bool TryUseModernFolderBrowser()
        {
            try
            {
                // This is a simplified approach - in a full implementation you might use
                // Windows.Storage.Pickers or other modern APIs
                return false; // For now, always use classic browser
            }
            catch
            {
                return false;
            }
        }

        private bool IsRunningInWinPE()
        {
            try
            {
                // Check for WinPE indicators
                // WinPE typically has X: as system drive and specific registry keys
                string systemDrive = Environment.GetEnvironmentVariable("SystemDrive") ?? "";
                bool isXDrive = systemDrive.Equals("X:", StringComparison.OrdinalIgnoreCase);

                // Check for WinPE-specific paths
                bool hasWinPEPaths = Directory.Exists(@"X:\Windows") ||
                                   Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE") != null &&
                                   File.Exists(@"X:\Windows\System32\winpeshl.exe");

                return isXDrive || hasWinPEPaths;
            }
            catch
            {
                return false;
            }
        }

        private void txtWindowsPath_TextChanged(object sender, EventArgs e)
        {
            UpdateUI();
        }

        private void UpdateUI()
        {
            try
            {
                bool hasValidPath = !string.IsNullOrWhiteSpace(txtWindowsPath.Text);
                bool hasValidUsername = !string.IsNullOrWhiteSpace(txtUsername.Text);

                if (hasValidPath)
                {
                    _accountCreator = new UserAccountCreator(txtWindowsPath.Text);
                    bool isValidWindows = _accountCreator.ValidateWindowsPath();

                    lblWindowsVersion.Text = isValidWindows ?
                        _accountCreator.GetWindowsVersionInfo() :
                        "Invalid Windows installation";

                    lblWindowsVersion.ForeColor = isValidWindows ?
                        System.Drawing.Color.Green :
                        System.Drawing.Color.Red;

                    btnCreateAccount.Enabled = isValidWindows && hasValidUsername;
                }
                else
                {
                    lblWindowsVersion.Text = "No Windows path selected";
                    lblWindowsVersion.ForeColor = System.Drawing.Color.Gray;
                    btnCreateAccount.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                lblWindowsVersion.Text = $"Error: {ex.Message}";
                lblWindowsVersion.ForeColor = System.Drawing.Color.Red;
                btnCreateAccount.Enabled = false;

                System.Diagnostics.Debug.WriteLine($"UpdateUI Error: {ex}");
            }
        }

        private void txtUsername_TextChanged(object sender, EventArgs e)
        {
            UpdateUI();
        }

        private async void btnCreateAccount_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate inputs
                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;
                string fullName = txtFullName.Text.Trim();
                bool isAdmin = chkIsAdmin.Checked;

                if (string.IsNullOrWhiteSpace(username))
                {
                    MessageBox.Show("Please enter a username.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (username.Length > 20)
                {
                    MessageBox.Show("Username cannot be longer than 20 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                // Check for invalid characters
                char[] invalidChars = { '\\', '/', ':', '*', '?', '"', '<', '>', '|' };
                if (username.IndexOfAny(invalidChars) >= 0)
                {
                    MessageBox.Show("Username contains invalid characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrEmpty(fullName))
                {
                    fullName = username;
                }

                // Confirm the action
                string confirmMessage = $"Create user account with the following details?\n\n" +
                    $"Username: {username}\n" +
                    $"Full Name: {fullName}\n" +
                    $"Administrator: {(isAdmin ? "Yes" : "No")}\n" +
                    $"Windows Path: {txtWindowsPath.Text}";

                if (MessageBox.Show(confirmMessage, "Confirm Account Creation",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                {
                    return;
                }

                // Disable UI during operation
                SetUIEnabled(false);
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                lblStatus.Text = "Creating user account...";

                // Create account asynchronously
                bool success = await Task.Run(() =>
                {
                    return _accountCreator!.CreateUserAccount(username, password, fullName, isAdmin,
                        (status) => {
                            Invoke(new Action(() => {
                                lblStatus.Text = status;
                            }));
                        });
                });

                if (success)
                {
                    lblStatus.Text = "User account created successfully!";
                    lblStatus.ForeColor = System.Drawing.Color.Green;

                    MessageBox.Show(
                        $"User account '{username}' has been created successfully!\n\n" +
                        "The account will be available when Windows boots normally.",
                        "Success",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    // Clear form
                    ClearForm();
                }
                else
                {
                    lblStatus.Text = "Failed to create user account.";
                    lblStatus.ForeColor = System.Drawing.Color.Red;
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error: {ex.Message}";
                lblStatus.ForeColor = System.Drawing.Color.Red;

                MessageBox.Show(
                    $"An error occurred while creating the user account:\n\n{ex.Message}",
                    "Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            finally
            {
                SetUIEnabled(true);
                progressBar.Visible = false;
            }
        }

        private void SetUIEnabled(bool enabled)
        {
            txtWindowsPath.Enabled = enabled;
            btnBrowseWindows.Enabled = enabled;
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            txtFullName.Enabled = enabled;
            chkIsAdmin.Enabled = enabled;
            btnCreateAccount.Enabled = enabled && !string.IsNullOrWhiteSpace(txtUsername.Text);
        }

        private void ClearForm()
        {
            txtUsername.Clear();
            txtPassword.Clear();
            txtFullName.Clear();
            chkIsAdmin.Checked = false;
            lblStatus.Text = "Ready";
            lblStatus.ForeColor = System.Drawing.Color.Black;
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            lblStatus.Text = "Ready";
            lblStatus.ForeColor = System.Drawing.Color.Black;
        }
    }
}
