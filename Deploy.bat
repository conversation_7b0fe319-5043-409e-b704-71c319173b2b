@echo off
echo ========================================
echo Create BBY TEST Account Master - Deployment
echo ========================================
echo.

echo Building application...
"C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" createBBYTESTAccount_Master.sln /p:Configuration=Release

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.
echo Files created in bin\Release\:
dir bin\Release\*.exe /b
dir bin\Release\*.config /b

echo.
echo ========================================
echo Deployment Instructions:
echo ========================================
echo 1. Copy createBBYTESTAccount_Master.exe to your WinPE environment
echo 2. Copy createBBYTESTAccount_Master.exe.config to the same location
echo 3. Ensure .NET Framework 4.8 is available in WinPE
echo 4. Run with administrator privileges
echo.
echo For WinPE deployment, you may need to:
echo - Add .NET Framework support to your WinPE image
echo - Ensure the application runs with elevated privileges
echo.
pause
